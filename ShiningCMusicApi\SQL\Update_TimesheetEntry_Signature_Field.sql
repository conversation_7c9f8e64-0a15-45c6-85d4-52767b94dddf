USE [MusicSchool]
GO

-- Update TimesheetEntry Signature field to NVARCHAR(MAX) for digital signature storage
-- This allows storing base64 encoded signature image data

PRINT 'Starting TimesheetEntry Signature field update...'

-- Check if the column exists and get current data type
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'TimesheetEntries' 
           AND COLUMN_NAME = 'Signature' 
           AND TABLE_SCHEMA = 'dbo')
BEGIN
    PRINT 'Signature column found. Checking current data type...'
    
    DECLARE @CurrentDataType NVARCHAR(50)
    SELECT @CurrentDataType = DATA_TYPE + '(' + 
           CASE 
               WHEN CHARACTER_MAXIMUM_LENGTH = -1 THEN 'MAX'
               WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CAST(CHARACTER_MAXIMUM_LENGTH AS NVARCHAR(10))
               ELSE 'N/A'
           END + ')'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'TimesheetEntries' 
    AND COLUMN_NAME = 'Signature' 
    AND TABLE_SCHEMA = 'dbo'
    
    PRINT 'Current Signature field data type: ' + @CurrentDataType
    
    -- Only update if not already NVARCHAR(MAX)
    IF @CurrentDataType != 'nvarchar(MAX)'
    BEGIN
        PRINT 'Updating Signature field from ' + @CurrentDataType + ' to NVARCHAR(MAX)...'
        
        -- Alter the column to NVARCHAR(MAX)
        ALTER TABLE [dbo].[TimesheetEntries] 
        ALTER COLUMN [Signature] NVARCHAR(MAX) NULL
        
        PRINT 'Signature field successfully updated to NVARCHAR(MAX)'
    END
    ELSE
    BEGIN
        PRINT 'Signature field is already NVARCHAR(MAX). No update needed.'
    END
END
ELSE
BEGIN
    PRINT 'ERROR: Signature column not found in TimesheetEntries table!'
END

PRINT 'TimesheetEntry Signature field update completed.'
GO
