@page "/timesheets"
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Components
@using ShiningCMusicApp.Components.Buttons
@using ShiningCMusicApp.Components.Dialogs
@using ShiningCMusicApp.Authorization
@using ShiningCMusicCommon.Constants
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Calendars
@using Microsoft.AspNetCore.Authorization
@attribute [RequireLevel20Access]
@inherits TimesheetsBase

<PageTitle>Timesheet Management</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">📋 <span class="d-none d-sm-inline">Timesheet Management</span><span class="d-sm-none">Timesheets</span></h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading timesheets...</p>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0">Timesheets</h5>
                            <PageActionButtonGroup ShowThirdButton="@CanCreateTimesheets"
                                                   ThirdTextDesktop="Add New Timesheet"
                                                   ThirdTextMobile="Add Timesheet"
                                                   ThirdIcon="bi bi-clipboard-plus"
                                                   OnThirdClick="OpenCreateModal"
                                                   FourthText="Refresh"
                                                   FourthIcon="bi bi-arrow-clockwise"
                                                   OnFourthClick="RefreshData" />
                        </div>
                    </div>
                    <div class="card-body">
                        <SfGrid DataSource="@timesheets" 
                                AllowPaging="true" 
                                AllowSorting="true" 
                                AllowFiltering="true" 
                                AllowResizing="true" 
                                GridLines="GridLine.Both"
                                AllowMultiSorting="false"
                                EnableAdaptiveUI="true"
                                AdaptiveUIMode="AdaptiveMode.Mobile"
                                Height="900">
                            <GridPageSettings PageSize="20"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.CheckBox"></GridFilterSettings>
                            <GridSortSettings>
                                <GridSortColumns>
                                    <GridSortColumn Field="@nameof(Timesheet.CreatedUTC)" Direction="SortDirection.Descending"></GridSortColumn>
                                </GridSortColumns>
                            </GridSortSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Timesheet.StudentName) HeaderText="Student Name" Width="150"></GridColumn>
                                @if (CanEditTimesheets)
                                {
                                    <GridColumn Field=@nameof(Timesheet.TutorName) HeaderText="Tutor Name" Width="150"></GridColumn>
                                }
                                <GridColumn Field=@nameof(Timesheet.StartDate) HeaderText="Start Date" Width="120" Format="dd/MM/yyyy" Type="ColumnType.Date"></GridColumn>
                                <GridColumn Field=@nameof(Timesheet.ClassDurationMinutes) HeaderText="Duration (min)" Width="120"></GridColumn>
                                <GridColumn Field=@nameof(Timesheet.ContactNumber) HeaderText="Contact" Width="120"></GridColumn>
                                <GridColumn Field=@nameof(Timesheet.CreatedUTC) HeaderText="Created" Width="120" Format="dd/MM/yyyy" Type="ColumnType.Date"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="200" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var timesheet = (context as Timesheet);
                                        }
                                        <GridActionButtonGroup ShowFirstButton="true"
                                                               FirstText="View Details"
                                                               FirstIcon="bi bi-card-list"
                                                               FirstTitle="View Timesheet Entries"
                                                               OnFirstClick="() => ViewTimesheet(timesheet)"
                                                               ThirdText="Edit"
                                                               ThirdIcon="bi bi-pencil"
                                                               ThirdTitle="Edit Timesheet"
                                                               IsThirdDisabled="@(!CanEditTimesheets)"
                                                               OnThirdClick="() => OpenEditModal(timesheet)"
                                                               FourthText="Delete"
                                                               FourthIcon="bi bi-trash"
                                                               FourthTitle="Delete Timesheet"
                                                               OnFourthClick="() => DeleteTimesheet(timesheet)"
                                                               ShowActionButtonLabel="@ShowActionButtonLabel"
                                                               ButtonBaseClass="grid-action-btn grid-btn-third" />
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<SfDialog @bind-Visible="showModal" Header="@modalTitle" Width="600px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentTimesheet" OnValidSubmit="@SaveTimesheet">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Student *</label>
                        <SfDropDownList TValue="int" TItem="Student" @bind-Value="currentTimesheet.StudentId"
                                        DataSource="@students" Placeholder="Select Student">
                            <DropDownListFieldSettings Value="StudentId" Text="StudentName"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int" TItem="Student" ValueChange="@OnStudentChanged"></DropDownListEvents>
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Subject *</label>
                        <SfDropDownList TValue="int" TItem="Subject" @bind-Value="currentTimesheet.SubjectId"
                                        DataSource="@subjects" Placeholder="Select Subject">
                            <DropDownListFieldSettings Value="SubjectId" Text="SubjectName"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int" TItem="Subject" ValueChange="@OnSubjectChanged"></DropDownListEvents>
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Tutor *</label>
                        <SfDropDownList TValue="int" TItem="Tutor" @bind-Value="currentTimesheet.TutorId"
                                        DataSource="@tutorsFiltered" Placeholder="Select Tutor">
                            <DropDownListFieldSettings Value="TutorId" Text="TutorName"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Class Duration (minutes) *</label>
                        <SfNumericTextBox TValue="int" @bind-Value="currentTimesheet.ClassDurationMinutes"
                                          Min="15" Max="180" Step="15"></SfNumericTextBox>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Start Date *</label>
                        <SfDatePicker TValue="DateTime" @bind-Value="currentTimesheet.StartDate"
                                      Format="dd/MM/yyyy" Placeholder="Select start date"></SfDatePicker>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Contact Number</label>
                        <SfTextBox @bind-Value="currentTimesheet.ContactNumber" Placeholder="Contact number"></SfTextBox>
                    </div>
                    <div class="col-12">
                        <label class="form-label">Notes</label>
                        <SfTextBox @bind-Value="currentTimesheet.Notes" Multiline="true" Placeholder="Additional notes"></SfTextBox>
                    </div>
                    @if (!isEditMode)
                    {
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>Auto-create Attendance Records:</strong> Specify how many blank attendance records to create automatically with this timesheet.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Number of Attendance Records to Create</label>
                            <SfNumericTextBox TValue="int" @bind-Value="numberOfEntriesForNewTimesheet"
                                              Min="0" Max="50" Step="1"
                                              Placeholder="Enter number (0 for none)"></SfNumericTextBox>
                            <div class="form-text">
                                Leave as 0 to create timesheet without any attendance records. You can add them later.
                            </div>
                        </div>
                    }
                </div>
                <ModalActionButtonGroup FirstText="@(isEditMode ? "Update" : "Create")"
                                        FirstIcon="@(isEditMode ? "bi bi-pencil" : "bi bi-plus-circle")"
                                        FirstButtonType="submit"
                                        IsLoading="@isSaving"
                                        IsFirstDisabled="@isSaving"
                                        SecondText="Cancel"
                                        OnSecondClick="CloseModal" />
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- View Details Modal -->
<SfDialog @bind-Visible="showViewModal" Header="@($"Timesheet Details - {GetStudentName(selectedTimesheet?.StudentId)}")" Width="1200px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            @if (selectedTimesheet != null)
            {
                <div class="row g-3 mb-4">
                    <div class="col-md-4">
                        <strong>Student:</strong> @GetStudentName(selectedTimesheet.StudentId)
                    </div>
                    <div class="col-md-4">
                        <strong>Tutor:</strong> @GetTutorName(selectedTimesheet.TutorId)
                    </div>
                    <div class="col-md-4">
                        <strong>Start Date:</strong> @selectedTimesheet.StartDate.ToString("dd/MM/yyyy")
                    </div>
                    <div class="col-md-4">
                        <strong>Duration:</strong> @selectedTimesheet.ClassDurationMinutes minutes
                    </div>
                    <div class="col-md-6">
                        <strong>Contact:</strong> @selectedTimesheet.ContactNumber
                    </div>
                    <div class="col-md-6">
                        <strong>Created:</strong> @selectedTimesheet.CreatedUTC.ToString("dd/MM/yyyy")
                    </div>
                    @if (!string.IsNullOrEmpty(selectedTimesheet.Notes))
                    {
                        <div class="col-12">
                            <strong>Notes:</strong> @selectedTimesheet.Notes
                        </div>
                    }
                </div>

                <h6>Attendance Records</h6>
                <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end mb-3">
                    @if (CanCreateTimesheets)
                    {
                        <button class="btn btn-outline-primary" style="min-width: 160px;" @onclick="OpenAddEntryModal">
                            <i class="bi bi-clipboard-plus-fill" style="color: inherit;"></i>
                            <span class="ms-2">Bulk Create</span>
                        </button>
                        <button class="btn btn-primary" style="min-width: 160px;" @onclick="OpenSingleEntryModal">
                            <i class="bi bi-clipboard-plus" style="color: inherit;"></i>
                            <span class="ms-2">Add Single</span>
                        </button>
                    }
                    <button class="btn btn-success" style="min-width: 160px;" @onclick="ExportTimesheetToExcel">
                        <i class="bi bi-file-earmark-excel" style="color: inherit;"></i>
                        <span class="ms-2">Export to Excel</span>
                    </button>
                </div>

                <SfGrid DataSource="@timesheetEntries" AllowPaging="true" AllowSorting="true" Height="300">
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridSortSettings>
                        <GridSortColumns>
                            <GridSortColumn Field="@nameof(TimesheetEntry.RecordId)" Direction="SortDirection.Ascending"></GridSortColumn>
                        </GridSortColumns>
                    </GridSortSettings>
                    <GridColumns>
                        <GridColumn Field=@nameof(TimesheetEntry.RecordId) HeaderText="Record #" Width="80">
                            <Template>
                                @{
                                    var entry = (context as TimesheetEntry);
                                    var recordDisplay = entry?.RecordId?.ToString() ?? "";
                                }
                                @recordDisplay
                            </Template>
                        </GridColumn>
                        <GridColumn Field=@nameof(TimesheetEntry.AttendanceDateTime) HeaderText="Date & Time" Width="150">
                            <Template>
                                @{
                                    var entry = (context as TimesheetEntry);
                                    var dateTimeDisplay = entry?.AttendanceDateTime?.ToString("dd/MM/yyyy hh:mm tt") ?? "";
                                }
                                @dateTimeDisplay
                            </Template>
                        </GridColumn>
                        <GridColumn Field=@nameof(TimesheetEntry.IsPresent) HeaderText="Present" Width="80" Type="ColumnType.Boolean" TextAlign="TextAlign.Center">
                                <Template>
                                    @{
                                        var entry = (context as TimesheetEntry);
                                        var isPresent = entry?.IsPresent == true;
                                        var iconClass = isPresent ? "bi bi-check-circle-fill text-success" : "bi bi-x-circle-fill text-danger";
                                        var title = isPresent ? "Present" : "Absent";
                                    }
                                    <i class="@iconClass" title="@title" aria-label="@title"></i>
                                </Template>
                            </GridColumn>
                        <GridColumn Field=@nameof(TimesheetEntry.Signature) HeaderText="Signatures" Width="150">
                            <Template>
                                @{
                                    var entry = (context as TimesheetEntry);
                                    var hasTextSignature = !string.IsNullOrEmpty(entry?.Signature);
                                    var hasDigitalSignature = !string.IsNullOrEmpty(entry?.Signature64);
                                }
                                <div class="d-flex gap-2 align-items-center">
                                    @if (hasTextSignature)
                                    {
                                        <span class="badge bg-secondary" title="Text signature: @entry.Signature">
                                            <i class="bi bi-pencil-fill"></i> Text
                                        </span>
                                    }
                                    @if (hasDigitalSignature)
                                    {
                                        <span class="badge bg-primary" title="Digital signature available">
                                            <i class="bi bi-vector-pen"></i> Digital
                                        </span>
                                    }
                                    @if (!hasTextSignature && !hasDigitalSignature)
                                    {
                                        <span class="text-muted small">No signature</span>
                                    }
                                </div>
                            </Template>
                        </GridColumn>
                        <GridColumn Field=@nameof(TimesheetEntry.Notes) HeaderText="Notes" Width="200"></GridColumn>
                        <GridColumn HeaderText="Actions" Width="200" AllowFiltering="false" AllowSorting="false">
                            <Template>
                                @{
                                    var entry = (context as TimesheetEntry);
                                }
                                <div class="d-flex gap-1">
                                    <button class="btn btn-outline-primary btn-sm grid-action-btn grid-btn-half" @onclick="async () => await OpenEditEntryModal(entry)" title="Edit">
                                        <i class="bi bi-pencil" style="color: inherit;"></i>
                                        @if (ShowActionButtonLabel)
                                        {
                                            <span class="d-none d-lg-inline ms-1">Edit</span>
                                        }
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm grid-action-btn grid-btn-half" @onclick="() => DeleteTimesheetEntry(entry)" title="Delete" disabled="@(!CanDeleteTimesheetEntries)">
                                        <i class="bi bi-trash" style="color: inherit;"></i>
                                        @if (ShowActionButtonLabel)
                                        {
                                            <span class="d-none d-lg-inline ms-1">Delete</span>
                                        }
                                    </button>
                                </div>
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
            <ModalActionButtonGroup ShowFirstButton=false
                                    SecondText="Close"
                                    OnSecondClick="CloseViewModal" />
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Add/Edit Entry Modal -->
<SfDialog @bind-Visible="showEntryModal" Header="@entryModalTitle" Width="500px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentEntry" OnValidSubmit="@SaveTimesheetEntry">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                    @if (isEditEntryMode)
                    {
                        <div class="row g-3 mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Record #</label>
                                <SfNumericTextBox TValue="int?" @bind-Value="currentEntry.RecordId"
                                                  Min="1" Max="999" Placeholder="Record number"></SfNumericTextBox>
                            </div>
                        </div>
                    }
                    <div class="row g-3"> 
                        <div class="col-md-8">
                            <label class="form-label">Date & Time</label>
                            <SfDateTimePicker TValue="DateTime?"
                                              @bind-Value="currentEntry.AttendanceDateTime"
                                              CssClass="form-control"
                                              Format="dd/MM/yyyy hh:mm tt"
                                              TimeFormat="hh:mm tt"
                                              Step="5"
                                              Placeholder="Leave empty if not attended">
                            </SfDateTimePicker>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Present</label>
                            <SfCheckBox @bind-Checked="currentEntry.IsPresent" Label="Student was present"></SfCheckBox>
                        </div>
                        @* <div class="col-12">
                            <label class="form-label">Signature</label>
                            <SfTextBox @bind-Value="currentEntry.Signature" Placeholder="Signature or initials"></SfTextBox>
                        </div> *@
                        <div class="col-12">
                            <label class="form-label">Digital Signature</label>
                            <div class="signature-container border rounded p-2" style="background-color: #f8f9fa;">
                                <SfSignature @ref="signatureComponent"
                                           Height="200px"
                                           Width="100%"
                                           BackgroundColor="#ffffff"
                                           StrokeColor="#000000"
                                           MinStrokeWidth="1"
                                           MaxStrokeWidth="3"
                                           Velocity="0.7">
                                </SfSignature>
                                <div class="d-flex justify-content-between mt-2">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" @onclick="ClearSignature">
                                        <i class="bi bi-eraser"></i> Clear
                                    </button>
                                    <small class="text-muted align-self-center">Draw your signature above</small>
                                    @if (!string.IsNullOrEmpty(currentEntry.Signature64))
                                    {
                                        <small class="text-success align-self-center">
                                            <i class="bi bi-check-circle"></i> Digital signature saved
                                        </small>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Text Signature</label>
                            <SfTextBox @bind-Value="currentEntry.Signature" Placeholder="Signature or initials (optional)"></SfTextBox>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Notes</label>
                            <SfTextBox @bind-Value="currentEntry.Notes" Multiline="true" Placeholder="Additional notes"></SfTextBox>
                        </div>
                    </div>
                <ModalActionButtonGroup FirstText="@(isEditEntryMode ? "Update" : "Add")"
                                        FirstIcon="@(isEditEntryMode ? "bi bi-pencil" : "bi bi-plus-circle")"
                                        FirstButtonType="submit"
                                        IsLoading="@isSavingEntry"
                                        IsFirstDisabled="@isSavingEntry"
                                        SecondText="Cancel"
                                        OnSecondClick="CloseEntryModal" />
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Bulk Create Entries Modal -->
<SfDialog @bind-Visible="showBulkEntryModal" Header="Bulk Create Attendance Records" Width="500px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <div class="row g-3">
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        This will create multiple blank attendance records for this timesheet.
                        You can fill in the attendance details later by editing each record individually.
                    </div>
                </div>
                <div class="col-12">
                    <label class="form-label">Number of Records to Create *</label>
                    <SfNumericTextBox TValue="int" @bind-Value="numberOfRecords"
                                      Min="1" Max="50" Step="1"
                                      Placeholder="Enter number of records (1-50)"></SfNumericTextBox>
                    <div class="form-text">
                        Records will be created with sequential record numbers and empty attendance times.
                    </div>
                </div>
            </div>
            <ModalActionButtonGroup FirstText="Create"
                FirstIcon="bi bi-plus-circle"
                OnFirstClick="CreateBulkTimesheetEntries"
                IsLoading="@isSavingEntry"
                IsFirstDisabled="@isSavingEntry"
                SecondText="Cancel"
                OnSecondClick="CloseBulkEntryModal" />
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Required Dialog Components -->
<DeleteConfirmationDialog />
<AlertDialog />
